import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'p-survey-overview',
  styleUrl: 'p-survey-overview.css',
  shadow: true,
})
export class PSurveyOverview {
  @Prop() surveyId: string;
  @Prop() survey: any;

  render() {
    return (
      <div>
        <c-card>
          <l-row justifyContent="space-between">
            <div>
              <e-text variant="footnote">TOTAL RESPONSES</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0</e-text>
            </div>
            <div>
              <e-text variant="footnote">COMPLETION RATE</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0%</e-text>
            </div>
            <div>
              <e-text variant="footnote">AVERAGE TIME</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0m</e-text>
            </div>
          </l-row>
        </c-card>
      </div>
    );
  }
}
