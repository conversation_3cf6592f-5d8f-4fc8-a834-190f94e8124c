.centered {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 90vh;
  margin: 0 auto;
}

#ready-container {
  width: 100%;
  margin: 0 auto;
}

.export-option {
  text-align: center;
  padding: 1em;
  border: 1px solid var(--color__grey--200);
  border-radius: 8px;
  width: 30%;
}

.responses-table-placeholder {
  padding: 2em;
  text-align: center;
  background-color: var(--color__grey--25);
  border-radius: 8px;
}

#ready-container__main-content {
  width: calc(100% - 230px);
  margin-left: 210px;
  margin-top: 50px;
}

#error-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 50vh;
}
