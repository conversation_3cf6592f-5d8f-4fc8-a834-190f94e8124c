import { Component, FunctionalComponent, State, Prop, h } from '@stencil/core';
import { Store } from '../../../../global/script/store';
import { GetSurveyApi } from '../../../../global/script/helpers';

@Component({
  tag: 'v-survey-overview',
  styleUrl: 'v-survey-overview.css',
  shadow: true,
})
export class VSurveyOverview {
  @Prop() surveyId: string;
  @State() compState: string = 'fetching';
  @State() survey: any;
  componentWillLoad() {
    Store.activeView = 'surveyOverview';
    document.title = 'Survey Overview | Sensefolks';
    this.getSurvey();
  }

  async getSurvey() {
    let { success, message, payload } = await GetSurveyApi(this.surveyId);
    if (!success) {
      this.compState = 'error';
      return alert(message);
    }
    if (!payload) {
      return alert(message);
    }
    this.survey = payload;
    this.compState = 'ready';
  }

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseChoice') {
      return <e-pill color="blue">SenseChoice</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="turquoise">SenseQuery</e-pill>;
    }
  }

  Fetching: FunctionalComponent = () => (
    <div class="centered">
      <e-spinner theme="dark"></e-spinner>
    </div>
  );

  Ready: FunctionalComponent = () => (
    <div id="ready-container">
      <l-row align="flex-start">
        <p-survey-sidebar surveyId={this.surveyId} activePage="overview"></p-survey-sidebar>
        <div id="ready-container__main-content">
          <l-row align="flex-start">
            <e-text variant="footnote">{this.generateSurveyPill(this.survey.type)}</e-text>
            <l-row>
              <e-text variant="footnote">
                <e-link url={`/surveys/${this.surveyId}/edit`}>Edit</e-link>
              </e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              {(this.survey.distribution === 'embed' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/embed`}>Embed</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              {(this.survey.distribution === 'link' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/share`}>Share</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              <e-text variant="footnote">/</e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-text variant="footnote">
                <e-link theme="danger" url={`/surveys/${this.surveyId}/delete`}>
                  Delete
                </e-link>
              </e-text>
            </l-row>
          </l-row>
          <l-spacer value={0.5}></l-spacer>
          <e-text variant="heading">{this.survey.title}</e-text>
          <l-spacer value={1.5}></l-spacer>
          <div class="content">{this.renderOverviewContent()}</div>
        </div>
      </l-row>
    </div>
  );

  renderOverviewContent() {
    return (
      <div>
        <c-card>
          <l-row justifyContent="space-between">
            <div>
              <e-text variant="footnote">TOTAL RESPONSES</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0</e-text>
            </div>
            <div>
              <e-text variant="footnote">COMPLETION RATE</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0%</e-text>
            </div>
            <div>
              <e-text variant="footnote">AVERAGE TIME</e-text>
              <l-spacer value={0.25}></l-spacer>
              <e-text variant="display">0m</e-text>
            </div>
          </l-row>
        </c-card>{' '}
      </div>
    );
  }

  Error: FunctionalComponent = () => (
    <div id="error-container">
      <article>
        <e-text variant="display">Could not fetch survey details :(</e-text>
        <l-spacer value={1}></l-spacer>
        <e-text>
          If the issue persists, kindly visit the <e-link url="/support">Support page</e-link> and
          report the issue
        </e-text>
      </article>
    </div>
  );

  render() {
    return (
      <c-main variant="shallow">
        {this.compState === 'fetching' && <this.Fetching></this.Fetching>}
        {this.compState === 'ready' && <this.Ready></this.Ready>}
        {this.compState === 'error' && <this.Error></this.Error>}
      </c-main>
    );
  }
}
