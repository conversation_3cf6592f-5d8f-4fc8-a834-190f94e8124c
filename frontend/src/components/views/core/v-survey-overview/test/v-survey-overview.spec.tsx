import { newSpecPage } from '@stencil/core/testing';
import { VSurveyOverview } from '../v-survey-overview';

describe('v-survey-overview', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VSurveyOverview],
      html: `<v-survey-overview></v-survey-overview>`,
    });
    expect(page.root).toEqualHtml(`
      <v-survey-overview>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-survey-overview>
    `);
  });
});
