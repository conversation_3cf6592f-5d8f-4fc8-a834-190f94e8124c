#ready-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  max-width: none; /* Override any inherited max-width constraints */
}

#ready-container__main-content {
  margin-left: 230px;
  padding-top: 40px;
  padding-right: 2em;
  width: calc(100% - 230px);
  box-sizing: border-box;
}

.content {
  width: 100%;
}

.centered {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container article {
  text-align: center;
  max-width: 400px;
}
