#ready-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

#sidebar {
  position: fixed;
  width: 160px;
  border-right: var(--border);
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-top: 40px;
  background-color: var(--color__white);
  z-index: 10;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.nav-item {
  padding: 0.75em 1em;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: var(--color__grey--50);
}

.nav-item--active {
  background-color: var(--color__indigo--50);
  border-left: 3px solid var(--color__indigo--800);
  font-weight: bold;
}

#ready-container__main-content {
  margin-left: 160px;
  padding: 2em;
  width: calc(100% - 160px);
  box-sizing: border-box;
}

.content {
  width: 100%;
}

.centered {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container article {
  text-align: center;
  max-width: 400px;
}
