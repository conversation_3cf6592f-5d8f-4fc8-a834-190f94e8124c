#ready-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

#ready-container__main-content {
  margin-left: 230px;
  padding-top: 40px;
  width: 100%;
  box-sizing: border-box;
}

.content {
  width: 100%;
}

.centered {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

#error-container article {
  text-align: center;
  max-width: 400px;
}
