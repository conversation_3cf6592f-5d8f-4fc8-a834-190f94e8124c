import { Component, h, Prop, State, FunctionalComponent } from '@stencil/core';
import { GetSurveyApi } from '../../../../global/script/helpers';

@Component({
  tag: 'v-survey-export',
  styleUrl: 'v-survey-export.css',
  shadow: true,
})
export class VSurveyExport {
  @Prop() surveyId: string;
  @State() compState: string = 'fetching';
  @State() survey: any;

  componentWillLoad() {
    this.getSurvey();
  }

  async getSurvey() {
    let { success, message, payload } = await GetSurveyApi(this.surveyId);
    if (!success) {
      this.compState = 'error';
      return alert(message);
    }
    this.survey = payload;
    this.compState = 'ready';
  }

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseChoice') {
      return <e-pill color="blue">SenseChoice</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="cyan">SenseQuery</e-pill>;
    } else if (value === 'sensePriority') {
      return <e-pill color="teal">SensePriority</e-pill>;
    } else {
      return <e-pill>{value}</e-pill>;
    }
  }

  Fetching: FunctionalComponent = () => (
    <div class="centered">
      <e-spinner theme="dark"></e-spinner>
    </div>
  );

  Ready: FunctionalComponent = () => (
    <div id="ready-container">
      <l-row align="flex-start">
        <p-survey-sidebar surveyId={this.surveyId} activePage="export"></p-survey-sidebar>
        <div id="ready-container__main-content">
          <l-row align="flex-start">
            <e-text variant="footnote">{this.generateSurveyPill(this.survey.type)}</e-text>
            <l-row>
              <e-text variant="footnote">
                <e-link url={`/surveys/${this.surveyId}/edit`}>
                  <l-row justifyContent="flex-start" align="center">
                    <e-image src="../../../assets/icon/dark/edit-dark.svg" width="1em"></e-image>
                    <l-spacer variant="horizontal" value={0.25}></l-spacer>
                    Edit
                  </l-row>
                </e-link>
              </e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              {(this.survey.distribution === 'embed' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/embed`}>Embed</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              {(this.survey.distribution === 'link' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/share`}>Share</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              <e-text variant="footnote">/</e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-text variant="footnote">
                <e-link theme="danger" url={`/surveys/${this.surveyId}/delete`}>
                  Delete
                </e-link>
              </e-text>
            </l-row>
          </l-row>
          <l-spacer value={0.5}></l-spacer>
          <e-text variant="heading">{this.survey.title}</e-text>
          <l-spacer value={1.5}></l-spacer>
          <div class="content">
            <c-card>
              <l-spacer value={1}></l-spacer>
              <e-text>Choose the format and data you want to export:</e-text>
              <l-spacer value={2}></l-spacer>

              <l-row justifyContent="space-between">
                <div class="export-option">
                  <e-text variant="subheading">CSV Export</e-text>
                  <l-spacer value={0.5}></l-spacer>
                  <e-text variant="footnote">Raw response data in spreadsheet format</e-text>
                  <l-spacer value={1}></l-spacer>
                  <e-button variant="outline" disabled>
                    Download CSV
                  </e-button>
                </div>

                <div class="export-option">
                  <e-text variant="subheading">PDF Report</e-text>
                  <l-spacer value={0.5}></l-spacer>
                  <e-text variant="footnote">Formatted summary report with charts</e-text>
                  <l-spacer value={1}></l-spacer>
                  <e-button variant="outline" disabled>
                    Download PDF
                  </e-button>
                </div>

                <div class="export-option">
                  <e-text variant="subheading">JSON Data</e-text>
                  <l-spacer value={0.5}></l-spacer>
                  <e-text variant="footnote">Raw data in JSON format for developers</e-text>
                  <l-spacer value={1}></l-spacer>
                  <e-button variant="outline" disabled>
                    Download JSON
                  </e-button>
                </div>
              </l-row>

              <l-spacer value={2}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>

              <e-text variant="footnote">
                Export options will be enabled once you have survey responses. All exports include
                only the data you have permission to access.
              </e-text>
            </c-card>
          </div>
        </div>
      </l-row>
    </div>
  );

  Error: FunctionalComponent = () => (
    <div id="error-container">
      <article>
        <e-text variant="display">Could not fetch survey details :(</e-text>
        <l-spacer value={1}></l-spacer>
        <e-text>
          If the issue persists, kindly visit the <e-link url="/support">Support page</e-link> and
          report the issue
        </e-text>
      </article>
    </div>
  );

  render() {
    return (
      <c-main variant="shallow">
        {this.compState === 'fetching' && <this.Fetching></this.Fetching>}
        {this.compState === 'ready' && <this.Ready></this.Ready>}
        {this.compState === 'error' && <this.Error></this.Error>}
      </c-main>
    );
  }
}
